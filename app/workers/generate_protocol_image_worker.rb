# frozen_string_literal: true

class GenerateProtocolImageWorker
  include Sidekiq::Worker
  sidekiq_options queue: :critical, retry: 3,
                  lock: :until_and_while_executing,
                  on_conflict: :log,
                  unique_for: 10.seconds

  sidekiq_retries_exhausted do |msg, ex|
    Honeybadger.notify(ex, context: { args: msg["args"] })
    Rails.logger.error("GenerateProtocolImageWorker retries exhausted: #{ex.message}")
  end

  def perform(user_id)
    Honeybadger.context(user_id: user_id)

    return if user_id.blank?

    start_time = Time.zone.now

    user = User.find_by_id(user_id)
    if user.blank?
      Honeybadger.notify("User not found for user_id: #{user_id}")
      Rails.logger.warn("User not found for user_id: #{user_id}")
      return
    end

    # Find user's poster layout
    user_poster_layout = UserPosterLayout.find_by(entity: user)
    if user_poster_layout.blank?
      Honeybadger.notify("UserPosterLayout not found for user_id: #{user_id}")
      Rails.logger.warn("UserPosterLayout not found for user_id: #{user_id}")
      return
    end

    bearer_token = JsonWebToken.get_token_for_media_to_ror_auth
    payload = {
      user_id: user_id,
      bearer_token: bearer_token
    }

    begin
      payload_response = invoke_lambda(payload)

      # Parse the response from lambda
      payload_parsed_response = JSON.parse(payload_response.payload.read)

      # Check if lambda execution was successful
      if payload_parsed_response['statusCode'] != 200
        error_message = 'Unknown error'
        if payload_parsed_response['body'].is_a?(String)
          begin
            body_parsed = JSON.parse(payload_parsed_response['body'])
            error_message = body_parsed['message'] || 'Unknown error'
          rescue JSON::ParserError
            error_message = payload_parsed_response['body'] || 'Unknown error'
          end
        elsif payload_parsed_response['body'].is_a?(Hash)
          error_message = payload_parsed_response['body']['message'] || 'Unknown error'
        end
        Honeybadger.notify("Lambda execution failed for user #{user_id}: #{error_message}")
        Rails.logger.warn("Lambda execution failed for user #{user_id}: #{error_message}")
        return
      end

      response_body = JSON.parse(payload_parsed_response['body'])
    rescue => e
      Honeybadger.notify("Error generating protocol image for user #{user_id}: #{e.message}")
      Rails.logger.warn("Failed to generate protocol image for user #{user_id}: #{e.message}")
      return
    end

    protocol_url = response_body['protocol_url']
    unless protocol_url.present?
      Honeybadger.notify("Missing protocol_url in lambda response for user #{user_id}: #{response_body}")
      Rails.logger.warn("Missing protocol_url in lambda response for user #{user_id}: #{response_body}")
      return
    end

    # Update the user poster layout with the protocol photo URL
    user_poster_layout.update!(video_frame_protocol_photo_url: protocol_url)
    user.deactivate_existing_user_video_posters
    elapsed_time = Time.zone.now - start_time
    Rails.logger.info("Protocol image generated successfully for user #{user_id} in #{elapsed_time} seconds")
    Rails.logger.info("Protocol URL: #{protocol_url}")
  end

  private

  def invoke_lambda(payload)
    Aws::Lambda::Client.new(
      region: 'ap-south-1',
      credentials: Aws::Credentials.new(
        Rails.application.credentials[:aws_access_key_id],
        Rails.application.credentials[:aws_secret_access_key]
      )
    ).invoke(
      function_name: 'arn:aws:lambda:ap-south-1:666527360739:function:UserProtocolPhotoGenerati-UserProtocolPhotoGenerat-t8TQt2zcsqvh', # TODO: Replace with actual protocol generation lambda ARN
      invocation_type: 'RequestResponse',
      payload: payload.to_json
    )
  end
end
