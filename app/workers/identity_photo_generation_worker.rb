# frozen_string_literal: true

class IdentityPhotoGenerationWorker
  include Sidekiq::Worker
  include Sidekiq::Throttled::Worker
  include IdentityPhotoGeneration
  
  sidekiq_options queue: :video_posters_generation, retry: 3, lock: :until_and_while_executing, on_conflict: :log
  

  sidekiq_retries_exhausted do |msg, ex|
    Honeybadger.notify(ex, context: { args: msg["args"] })
    Rails.logger.error("IdentityPhotoGenerationWorker retries exhausted: #{ex.message}")
  end

  def perform(user_id)
    Honeybadger.context({ user_id: user_id })

    user = User.find_by_id(user_id)
    return if user.blank?

    # Get all active video frames
    video_frames = VideoFrame.where(active: true)
    return if video_frames.empty?

    # Generate new identity images for each video frame type
    video_frames.each do |video_frame|
      begin
        # Get existing active frame for this user and video frame
        existing_frame = UserVideoFrame.find_by(user: user, video_frame: video_frame, active: true)
        next if existing_frame.blank?

        # Generate new identity image
        identity_image_url = generate_identity_image(user, video_frame)

        # Create new user video frame with the generated identity image URL
        existing_frame.update!(identity_photo_url: identity_image_url)

        Rails.logger.info("New identity image generated for user #{user_id}, video_frame #{video_frame.id}")

      rescue StandardError => e
        # If generation fails for this frame, log error but continue with other frames
        Honeybadger.notify(e, context: { user_id: user_id, video_frame_id: video_frame.id })
        Rails.logger.error("Failed to generate identity image for user #{user_id}, video_frame #{video_frame.id}: #{e.message}")
      end
    end

    # update user_video_frame with active: false
    user.deactivate_existing_user_video_posters

  rescue StandardError => e
    Honeybadger.notify(e, context: { user_id: user_id })
    Rails.logger.error("IdentityImagesWorker failed: #{e.message}")
    raise
  end

end
