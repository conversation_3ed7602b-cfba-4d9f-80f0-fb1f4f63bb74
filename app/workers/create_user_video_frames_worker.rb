# frozen_string_literal: true

class CreateUserVideoFramesWorker
  include Sidekiq::Worker
  include IdentityPhotoGeneration

  sidekiq_options queue: :video_posters_generation, retry: 3, lock: :until_and_while_executing, on_conflict: :log
  
  sidekiq_retries_exhausted do |msg, ex|
    Honeybadger.notify(ex, context: { args: msg["args"] })
    Rails.logger.error("CreateUserVideoFramesWorker retries exhausted: #{ex.message}")
  end

  def perform(user_id)
    Honeybadger.context({ user_id: user_id })

    user = User.find_by_id(user_id)
    return if user.blank?

    Rails.logger.info("Creating user video frames for user #{user_id}")

    # Get all active video frames
    video_frames = VideoFrame.where(active: true)
    return if video_frames.empty?

    created_frames = []

    # Create UserVideoFrame for each active video frame type if it doesn't exist
        video_frames.each do |video_frame|
      begin
        # Check if UserVideoFrame already exists
        existing_frame = UserVideoFrame.find_by(user: user, video_frame: video_frame, active: true)

        if existing_frame.blank?
          # Generate identity photo for this video frame
          identity_photo_url = generate_identity_image(user, video_frame)

          # Create new user video frame with the generated identity photo URL
          user_video_frame = UserVideoFrame.create!(
            user: user,
            video_frame: video_frame,
            identity_photo_url: identity_photo_url,
            active: true
          )

          created_frames << user_video_frame
          Rails.logger.info("Created UserVideoFrame #{user_video_frame.id} for user #{user_id}, video_frame #{video_frame.id} with identity photo")
        else
          Rails.logger.info("UserVideoFrame already exists for user #{user_id}, video_frame #{video_frame.id}")
        end

      rescue StandardError => e
        # If creation fails for this frame, log error but continue with other frames
        Honeybadger.notify(e, context: { user_id: user_id, video_frame_id: video_frame.id })
        Rails.logger.error("Failed to create UserVideoFrame for user #{user_id}, video_frame #{video_frame.id}: #{e.message}")
      end
    end

    if created_frames.any?
      Rails.logger.info("Successfully created #{created_frames.count} video frames with identity photos for user #{user_id}")

      # Update user_video_poster with active: false (following existing pattern)
      user.deactivate_existing_user_video_posters      
    end

  rescue StandardError => e
    Honeybadger.notify(e, context: { user_id: user_id })
    Rails.logger.error("CreateUserVideoFramesWorker failed for user #{user_id}: #{e.message}")
    raise
  end

  # All identity photo generation methods are now provided by the IdentityPhotoGeneration concern
end
