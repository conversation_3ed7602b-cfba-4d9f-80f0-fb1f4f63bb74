class UserVideoFrame < ApplicationRecord
  belongs_to :user
  belongs_to :video_frame

  validates :user_id, presence: true
  validates :video_frame_id, presence: true
  validates :identity_photo_url, presence: true

  has_many :user_video_posters, dependent: :destroy

  scope :active, -> { where(active: true) }

  has_paper_trail


  FRAME_DIMENSIONS = {
    'PORTRAIT' => { width: 360, height: 550 },
    'LANDSCAPE' => { width: 360, height: 358 },
    'SQUARE' => { width: 360, height: 420 }
  }.freeze

  VIDEO_DIMENSIONS = {
    'PORTRAIT' => { width: 198, height: 352 },
    'LANDSCAPE' => { width: 330, height: 188 },
    'SQUARE' => { width: 330, height: 248 }
  }.freeze

  USER_PHOTO_DIMENSIONS = {
    'PORTRAIT' => { width: 180, height: 202 },
    'LANDSCAPE' => { width: 116, height: 130 },
    'SQUARE' => { width: 116, height: 130 }
  }.freeze

  # Spacing constants from commented constants
  FRAME_PADDING = 16
  VIDEO_PADDING = 15
  IDENTITY_SPACING = 8
  PROTOCOL_IMAGE_WIDTH = 360  # protocolImageWidth from comments (missing but inferred)
  PROTOCOL_IMAGE_HEIGHT = 86  # protocolImageHeight from comments
  IDENTITY_PLATE_WIDTH = 360
  IDENTITY_PLATE_HEIGHT = 50

    # Identity image dimensions for different orientations
  IDENTITY_IMAGE_DIMENSIONS = {
    'PORTRAIT' => { width: IDENTITY_PLATE_WIDTH, height: IDENTITY_PLATE_HEIGHT },
    'LANDSCAPE' => { width: IDENTITY_PLATE_WIDTH, height: IDENTITY_PLATE_HEIGHT },
    'SQUARE' => { width: IDENTITY_PLATE_WIDTH, height: IDENTITY_PLATE_HEIGHT }
  }.freeze

  # Identity image styling constants
  IDENTITY_TEXT_PADDING = 10
  IDENTITY_NAME_FONT_SIZE_LARGE = 30
  IDENTITY_NAME_FONT_SIZE_MEDIUM = 26
  IDENTITY_NAME_FONT_SIZE_SMALL = 22
  IDENTITY_NAME_FONT_SIZE_MIN = 18
  IDENTITY_BADGE_FONT_SIZE = 20
  IDENTITY_LEFT_PADDING_LANDSCAPE = 132  # Based on template: user photo width + padding

  # Font size thresholds for name length
  IDENTITY_NAME_LENGTH_MEDIUM_THRESHOLD = 12
  IDENTITY_NAME_LENGTH_SMALL_THRESHOLD = 16
  IDENTITY_NAME_LENGTH_MIN_THRESHOLD = 20

  def get_video_mode
    video_frame.video_type
  end

  def get_frame_dimensions(mode)
    dimensions = FRAME_DIMENSIONS[mode]
    raise ArgumentError, "Invalid video mode: #{mode}" unless dimensions
    dimensions
  end

  def get_video_creative_elements(source_video)
    mode = get_video_mode
    frame_dimensions = get_frame_dimensions(mode)
    video_dimensions = get_video_dimensions(mode)
    user_photo_dimensions = get_user_photo_dimensions(mode)

    video_creative_elements = if mode == "PORTRAIT"
                      [
                        get_user_photo_data(user_photo_dimensions, frame_dimensions),
                        get_identity_data(frame_dimensions)
                      ]
                    elsif mode == "LANDSCAPE" || "SQUARE"
                      [
                        get_identity_data(frame_dimensions),
                        get_user_photo_data(user_photo_dimensions, frame_dimensions)
                      ]
                    end
      [
        get_background_data(frame_dimensions),
        get_protocol_data,
        get_video_data(source_video, video_dimensions, frame_dimensions),
        *video_creative_elements
      ]
  end

  private

  def get_background_data(frame_dimensions)
    {
      "url": "https://a-cdn.thecircleapp.in/production/user-protocol-photos/tysrcp.png",
      "width": frame_dimensions[:width],
      "height": frame_dimensions[:height],
      "x": 0,
      "y": 0,
      "type": "photo",
      "is_user_photo": false
    }
  end

  def get_protocol_data
    {
      "url": get_protocl_photo_url || "https://a-cdn.thecircleapp.in/production/user-protocol-photos/protocol-298110-1748337600498.png",
      "width": PROTOCOL_IMAGE_WIDTH,
      "height": PROTOCOL_IMAGE_HEIGHT,
      "x": FRAME_PADDING,
      "y": FRAME_PADDING,
      "type": "photo",
      "is_user_photo": false
    }
  end

  def get_video_data(source_video, video_dimensions, frame_dimensions)
    mode = get_video_mode

    # Calculate video position based on mode
    x_position, y_position = calculate_video_position(mode, video_dimensions, frame_dimensions)

    video_url = source_video&.source_url || source_video&.url
    # Fallback URL if no video URL is available
    video_url ||= "https://ruv-cdn.thecircleapp.in/assets01/58be1c4819d4d7db78989b7cddc7ec82.mp4"

    {
      "url": video_url,
      "width": video_dimensions[:width],
      "height": video_dimensions[:height],
      "x": x_position,
      "y": y_position,
      "type": "video",
      "is_user_photo": false,
      "border": {
        "radius": 20,
        "size": 0,
        "color":  0xff000000
      }
    }
  end

  def get_user_photo_data(user_photo_dimensions, frame_dimensions)
    mode = get_video_mode

    # Calculate user photo position based on mode
    x_position, y_position = calculate_user_photo_position(mode, user_photo_dimensions, frame_dimensions)

    user_photo_url = user&.poster_photo&.url
    # Fallback URL if no user photo is available
    user_photo_url ||= "https://a-cdn.thecircleapp.in/production/photos/41/60ac97b3-6911-4816-b393-86b5086b976c.png"

    {
      "url": user_photo_url,
      "width": user_photo_dimensions[:width],
      "height": user_photo_dimensions[:height],
      "x": x_position,
      "y": y_position,
      "type": "photo",
      "is_user_photo": true
    }
  end

  def get_identity_data(frame_dimensions)
    identity_url = self.identity_photo_url
    # Fallback URL if no identity photo is available
    identity_url ||= "https://a-cdn.thecircleapp.in/production/user-protocol-photos/identity.png"

    {
      "url": identity_url,
      "width": IDENTITY_PLATE_WIDTH,
      "height": IDENTITY_PLATE_HEIGHT,
      "x": 0,
      "y": frame_dimensions[:height] - IDENTITY_PLATE_HEIGHT,
      "type": "photo",
      "is_user_photo": false
    }
  end


  def get_video_dimensions(mode)
    dimensions = VIDEO_DIMENSIONS[mode]
    raise ArgumentError, "Invalid video mode: #{mode}" unless dimensions
    dimensions
  end

  def get_user_photo_dimensions(mode)
    dimensions = USER_PHOTO_DIMENSIONS[mode]
    raise ArgumentError, "Invalid video mode: #{mode}" unless dimensions
    dimensions
  end

  # Position calculation methods
  def calculate_video_position(mode, video_dimensions, frame_dimensions)
    case mode
    when 'PORTRAIT'
      x = frame_dimensions[:width] - FRAME_PADDING - video_dimensions[:width]
      y = frame_dimensions[:height] - IDENTITY_PLATE_HEIGHT - IDENTITY_SPACING - video_dimensions[:height]
    when 'SQUARE'
      x = frame_dimensions[:width] - video_dimensions[:width] - VIDEO_PADDING
      y = frame_dimensions[:height] - video_dimensions[:height] - IDENTITY_PLATE_HEIGHT - VIDEO_PADDING
    when 'LANDSCAPE'
      x = frame_dimensions[:width] - video_dimensions[:width] - VIDEO_PADDING
      y = frame_dimensions[:height] - video_dimensions[:height] - IDENTITY_PLATE_HEIGHT - 9
    else
      raise ArgumentError, "Invalid video mode: #{mode}"
    end
    [x, y]
  end

  def calculate_user_photo_position(mode, user_photo_dimensions, frame_dimensions)
    case mode
    when 'PORTRAIT'
      x = 0
      y = frame_dimensions[:height] - IDENTITY_PLATE_HEIGHT - user_photo_dimensions[:height]
    when 'SQUARE', 'LANDSCAPE'
      x = 0
      y = frame_dimensions[:height] - user_photo_dimensions[:height]
    else
      raise ArgumentError, "Invalid video mode: #{mode}"
    end
    [x, y]
  end

  def get_protocl_photo_url
    UserPosterLayout.where(entity: user).last&.video_frame_protocol_photo_url
  end

end
